{"ast": null, "code": "var _jsxFileName = \"D:\\\\hazimpro\\\\cafe-app\\\\src\\\\components\\\\ui\\\\select.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$(),\n  _s5 = $RefreshSig$();\nimport * as React from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SelectContext = /*#__PURE__*/React.createContext(null);\nexport function Select({\n  onValueChange,\n  children\n}) {\n  _s();\n  const [value, setValue] = React.useState(\"\");\n  const [open, setOpen] = React.useState(false);\n  const handleValueChange = newValue => {\n    setValue(newValue);\n    onValueChange(newValue);\n    setOpen(false);\n  };\n  return /*#__PURE__*/_jsxDEV(SelectContext.Provider, {\n    value: {\n      value,\n      onValueChange: handleValueChange,\n      open,\n      setOpen\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative\",\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 5\n  }, this);\n}\n_s(Select, \"oiiG1WkpE0xKV7BWyXNS5gPp9fs=\");\n_c = Select;\nexport function SelectTrigger({\n  children\n}) {\n  _s2();\n  const context = React.useContext(SelectContext);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"border p-3 rounded cursor-pointer flex justify-between items-center bg-white hover:bg-gray-50\",\n    onClick: () => context === null || context === void 0 ? void 0 : context.setOpen(!context.open),\n    children: [children, /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"text-gray-400\",\n      children: \"\\u25BC\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 35,\n    columnNumber: 5\n  }, this);\n}\n_s2(SelectTrigger, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n_c2 = SelectTrigger;\nexport function SelectValue({\n  placeholder\n}) {\n  _s3();\n  const context = React.useContext(SelectContext);\n  return /*#__PURE__*/_jsxDEV(\"span\", {\n    className: context !== null && context !== void 0 && context.value ? \"text-black\" : \"text-gray-500\",\n    children: (context === null || context === void 0 ? void 0 : context.value) || placeholder\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 49,\n    columnNumber: 5\n  }, this);\n}\n_s3(SelectValue, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n_c3 = SelectValue;\nexport function SelectContent({\n  children\n}) {\n  _s4();\n  const context = React.useContext(SelectContext);\n  if (!(context !== null && context !== void 0 && context.open)) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"absolute top-full left-0 right-0 border mt-1 rounded bg-white shadow-lg z-50 max-h-60 overflow-y-auto\",\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 61,\n    columnNumber: 5\n  }, this);\n}\n_s4(SelectContent, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n_c4 = SelectContent;\nexport function SelectItem({\n  value,\n  children\n}) {\n  _s5();\n  const context = React.useContext(SelectContext);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"cursor-pointer hover:bg-gray-100 p-3 text-right\",\n    onClick: () => context === null || context === void 0 ? void 0 : context.onValueChange(value),\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 71,\n    columnNumber: 5\n  }, this);\n}\n_s5(SelectItem, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n_c5 = SelectItem;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"Select\");\n$RefreshReg$(_c2, \"SelectTrigger\");\n$RefreshReg$(_c3, \"SelectValue\");\n$RefreshReg$(_c4, \"SelectContent\");\n$RefreshReg$(_c5, \"SelectItem\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "SelectContext", "createContext", "Select", "onValueChange", "children", "_s", "value", "setValue", "useState", "open", "<PERSON><PERSON><PERSON>", "handleValueChange", "newValue", "Provider", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "SelectTrigger", "_s2", "context", "useContext", "onClick", "_c2", "SelectValue", "placeholder", "_s3", "_c3", "SelectContent", "_s4", "_c4", "SelectItem", "_s5", "_c5", "$RefreshReg$"], "sources": ["D:/hazimpro/cafe-app/src/components/ui/select.tsx"], "sourcesContent": ["import * as React from \"react\"\n\ninterface SelectContextType {\n  value: string;\n  onValueChange: (value: string) => void;\n  open: boolean;\n  setOpen: (open: boolean) => void;\n}\n\nconst SelectContext = React.createContext<SelectContextType | null>(null);\n\nexport function Select({ onValueChange, children }: { onValueChange: (value: string) => void, children: React.ReactNode }) {\n  const [value, setValue] = React.useState(\"\");\n  const [open, setOpen] = React.useState(false);\n\n  const handleValueChange = (newValue: string) => {\n    setValue(newValue);\n    onValueChange(newValue);\n    setOpen(false);\n  };\n\n  return (\n    <SelectContext.Provider value={{ value, onValueChange: handleValueChange, open, setOpen }}>\n      <div className=\"relative\">\n        {children}\n      </div>\n    </SelectContext.Provider>\n  );\n}\n\nexport function SelectTrigger({ children }: { children: React.ReactNode }) {\n  const context = React.useContext(SelectContext);\n\n  return (\n    <div\n      className=\"border p-3 rounded cursor-pointer flex justify-between items-center bg-white hover:bg-gray-50\"\n      onClick={() => context?.setOpen(!context.open)}\n    >\n      {children}\n      <span className=\"text-gray-400\">▼</span>\n    </div>\n  );\n}\n\nexport function SelectValue({ placeholder }: { placeholder: string }) {\n  const context = React.useContext(SelectContext);\n\n  return (\n    <span className={context?.value ? \"text-black\" : \"text-gray-500\"}>\n      {context?.value || placeholder}\n    </span>\n  );\n}\n\nexport function SelectContent({ children }: { children: React.ReactNode }) {\n  const context = React.useContext(SelectContext);\n\n  if (!context?.open) return null;\n\n  return (\n    <div className=\"absolute top-full left-0 right-0 border mt-1 rounded bg-white shadow-lg z-50 max-h-60 overflow-y-auto\">\n      {children}\n    </div>\n  );\n}\n\nexport function SelectItem({ value, children }: { value: string, children: React.ReactNode }) {\n  const context = React.useContext(SelectContext);\n\n  return (\n    <div\n      className=\"cursor-pointer hover:bg-gray-100 p-3 text-right\"\n      onClick={() => context?.onValueChange(value)}\n    >\n      {children}\n    </div>\n  );\n}\n"], "mappings": ";;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAS9B,MAAMC,aAAa,gBAAGH,KAAK,CAACI,aAAa,CAA2B,IAAI,CAAC;AAEzE,OAAO,SAASC,MAAMA,CAAC;EAAEC,aAAa;EAAEC;AAAgF,CAAC,EAAE;EAAAC,EAAA;EACzH,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGV,KAAK,CAACW,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGb,KAAK,CAACW,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAMG,iBAAiB,GAAIC,QAAgB,IAAK;IAC9CL,QAAQ,CAACK,QAAQ,CAAC;IAClBT,aAAa,CAACS,QAAQ,CAAC;IACvBF,OAAO,CAAC,KAAK,CAAC;EAChB,CAAC;EAED,oBACEX,OAAA,CAACC,aAAa,CAACa,QAAQ;IAACP,KAAK,EAAE;MAAEA,KAAK;MAAEH,aAAa,EAAEQ,iBAAiB;MAAEF,IAAI;MAAEC;IAAQ,CAAE;IAAAN,QAAA,eACxFL,OAAA;MAAKe,SAAS,EAAC,UAAU;MAAAV,QAAA,EACtBA;IAAQ;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACgB,CAAC;AAE7B;AAACb,EAAA,CAjBeH,MAAM;AAAAiB,EAAA,GAANjB,MAAM;AAmBtB,OAAO,SAASkB,aAAaA,CAAC;EAAEhB;AAAwC,CAAC,EAAE;EAAAiB,GAAA;EACzE,MAAMC,OAAO,GAAGzB,KAAK,CAAC0B,UAAU,CAACvB,aAAa,CAAC;EAE/C,oBACED,OAAA;IACEe,SAAS,EAAC,+FAA+F;IACzGU,OAAO,EAAEA,CAAA,KAAMF,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEZ,OAAO,CAAC,CAACY,OAAO,CAACb,IAAI,CAAE;IAAAL,QAAA,GAE9CA,QAAQ,eACTL,OAAA;MAAMe,SAAS,EAAC,eAAe;MAAAV,QAAA,EAAC;IAAC;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACrC,CAAC;AAEV;AAACG,GAAA,CAZeD,aAAa;AAAAK,GAAA,GAAbL,aAAa;AAc7B,OAAO,SAASM,WAAWA,CAAC;EAAEC;AAAqC,CAAC,EAAE;EAAAC,GAAA;EACpE,MAAMN,OAAO,GAAGzB,KAAK,CAAC0B,UAAU,CAACvB,aAAa,CAAC;EAE/C,oBACED,OAAA;IAAMe,SAAS,EAAEQ,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEhB,KAAK,GAAG,YAAY,GAAG,eAAgB;IAAAF,QAAA,EAC9D,CAAAkB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEhB,KAAK,KAAIqB;EAAW;IAAAZ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC1B,CAAC;AAEX;AAACU,GAAA,CAReF,WAAW;AAAAG,GAAA,GAAXH,WAAW;AAU3B,OAAO,SAASI,aAAaA,CAAC;EAAE1B;AAAwC,CAAC,EAAE;EAAA2B,GAAA;EACzE,MAAMT,OAAO,GAAGzB,KAAK,CAAC0B,UAAU,CAACvB,aAAa,CAAC;EAE/C,IAAI,EAACsB,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEb,IAAI,GAAE,OAAO,IAAI;EAE/B,oBACEV,OAAA;IAAKe,SAAS,EAAC,uGAAuG;IAAAV,QAAA,EACnHA;EAAQ;IAAAW,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV;AAACa,GAAA,CAVeD,aAAa;AAAAE,GAAA,GAAbF,aAAa;AAY7B,OAAO,SAASG,UAAUA,CAAC;EAAE3B,KAAK;EAAEF;AAAuD,CAAC,EAAE;EAAA8B,GAAA;EAC5F,MAAMZ,OAAO,GAAGzB,KAAK,CAAC0B,UAAU,CAACvB,aAAa,CAAC;EAE/C,oBACED,OAAA;IACEe,SAAS,EAAC,iDAAiD;IAC3DU,OAAO,EAAEA,CAAA,KAAMF,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEnB,aAAa,CAACG,KAAK,CAAE;IAAAF,QAAA,EAE5CA;EAAQ;IAAAW,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV;AAACgB,GAAA,CAXeD,UAAU;AAAAE,GAAA,GAAVF,UAAU;AAAA,IAAAd,EAAA,EAAAM,GAAA,EAAAI,GAAA,EAAAG,GAAA,EAAAG,GAAA;AAAAC,YAAA,CAAAjB,EAAA;AAAAiB,YAAA,CAAAX,GAAA;AAAAW,YAAA,CAAAP,GAAA;AAAAO,YAAA,CAAAJ,GAAA;AAAAI,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}