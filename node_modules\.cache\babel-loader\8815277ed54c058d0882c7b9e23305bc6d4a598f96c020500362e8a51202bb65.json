{"ast": null, "code": "var _jsxFileName = \"D:\\\\hazimpro\\\\cafe-app\\\\src\\\\TestApp.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport { useState } from \"react\";\nimport { Button } from \"./components/ui/button\";\nimport { Input } from \"./components/ui/input\";\nimport { Label } from \"./components/ui/label\";\n\n// مكون تسجيل الدخول البسيط\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction SimpleLoginScreen({\n  onLogin\n}) {\n  _s();\n  const [subscriptionCode, setSubscriptionCode] = useState(\"\");\n  const [error, setError] = useState(\"\");\n  const [isLoading, setIsLoading] = useState(false);\n  const handleLogin = () => {\n    setIsLoading(true);\n    setError(\"\");\n\n    // محاكاة التحقق من رمز الاشتراك\n    setTimeout(() => {\n      if (subscriptionCode === \"VIP123\") {\n        onLogin(true);\n      } else {\n        setError(\"رمز الاشتراك غير صحيح. جرب: VIP123\");\n        onLogin(false);\n      }\n      setIsLoading(false);\n    }, 1000);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100\",\n    dir: \"rtl\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white p-8 rounded-lg shadow-lg w-full max-w-md\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-gray-800 mb-2\",\n          children: \"\\u2615 \\u0645\\u0642\\u0647\\u0649 VIP\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"\\u0623\\u062F\\u062E\\u0644 \\u0631\\u0645\\u0632 \\u0627\\u0644\\u0627\\u0634\\u062A\\u0631\\u0627\\u0643 \\u0644\\u0644\\u062F\\u062E\\u0648\\u0644\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Label, {\n            htmlFor: \"subscription-code\",\n            className: \"text-right block mb-2\",\n            children: \"\\u0631\\u0645\\u0632 \\u0627\\u0644\\u0627\\u0634\\u062A\\u0631\\u0627\\u0643\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            id: \"subscription-code\",\n            type: \"text\",\n            placeholder: \"\\u0623\\u062F\\u062E\\u0644 \\u0631\\u0645\\u0632 \\u0627\\u0644\\u0627\\u0634\\u062A\\u0631\\u0627\\u0643 (\\u0645\\u062B\\u0627\\u0644: VIP123)\",\n            value: subscriptionCode,\n            onChange: e => setSubscriptionCode(e.target.value),\n            className: \"text-center text-lg\",\n            onKeyPress: e => e.key === 'Enter' && handleLogin()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded text-center\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleLogin,\n          disabled: !subscriptionCode || isLoading,\n          className: \"w-full text-lg py-3\",\n          children: isLoading ? \"جاري التحقق...\" : \"🔓 دخول\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center text-sm text-gray-500 mt-4\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"\\uD83D\\uDCA1 \\u062A\\u0644\\u0645\\u064A\\u062D: \\u0627\\u0633\\u062A\\u062E\\u062F\\u0645 \\u0627\\u0644\\u0631\\u0645\\u0632 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"VIP123\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 39\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 5\n  }, this);\n}\n\n// التطبيق الرئيسي المبسط\n_s(SimpleLoginScreen, \"RDTlTpzcVzK5ZSENApoExtgPY3g=\");\n_c = SimpleLoginScreen;\nexport default function TestApp() {\n  _s2();\n  const [isLoggedIn, setIsLoggedIn] = useState(false);\n  const handleLogin = success => {\n    if (success) {\n      setIsLoggedIn(true);\n    }\n  };\n  const handleLogout = () => {\n    setIsLoggedIn(false);\n  };\n\n  // إذا لم يسجل الدخول، اعرض شاشة تسجيل الدخول\n  if (!isLoggedIn) {\n    return /*#__PURE__*/_jsxDEV(SimpleLoginScreen, {\n      onLogin: handleLogin\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 12\n    }, this);\n  }\n\n  // واجهة إدارة المقهى الرئيسية\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-6 max-w-4xl mx-auto text-right\",\n    dir: \"rtl\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-2xl font-bold\",\n        children: \"\\uD83C\\uDF7D\\uFE0F \\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0637\\u0627\\u0648\\u0644\\u0627\\u062A - \\u0645\\u0642\\u0647\\u0649 VIP\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline\",\n        onClick: handleLogout,\n        className: \"text-red-600 hover:text-red-700\",\n        children: \"\\uD83D\\uDEAA \\u062A\\u0633\\u062C\\u064A\\u0644 \\u062E\\u0631\\u0648\\u062C\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-green-50 p-6 rounded-lg text-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-xl font-bold text-green-800 mb-2\",\n        children: \"\\uD83C\\uDF89 \\u062A\\u0645 \\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062F\\u062E\\u0648\\u0644 \\u0628\\u0646\\u062C\\u0627\\u062D!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-green-700\",\n        children: \"\\u0645\\u0631\\u062D\\u0628\\u0627\\u064B \\u0628\\u0643 \\u0641\\u064A \\u0646\\u0638\\u0627\\u0645 \\u0625\\u062F\\u0627\\u0631\\u0629 \\u0645\\u0642\\u0647\\u0649 VIP\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 96,\n    columnNumber: 5\n  }, this);\n}\n_s2(TestApp, \"g0MSgNVZk+vKiEFnDJ9VPEfswFA=\");\n_c2 = TestApp;\nvar _c, _c2;\n$RefreshReg$(_c, \"SimpleLoginScreen\");\n$RefreshReg$(_c2, \"TestApp\");", "map": {"version": 3, "names": ["useState", "<PERSON><PERSON>", "Input", "Label", "jsxDEV", "_jsxDEV", "SimpleLoginScreen", "onLogin", "_s", "subscriptionCode", "setSubscriptionCode", "error", "setError", "isLoading", "setIsLoading", "handleLogin", "setTimeout", "className", "dir", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "htmlFor", "id", "type", "placeholder", "value", "onChange", "e", "target", "onKeyPress", "key", "onClick", "disabled", "_c", "TestApp", "_s2", "isLoggedIn", "setIsLoggedIn", "success", "handleLogout", "variant", "_c2", "$RefreshReg$"], "sources": ["D:/hazimpro/cafe-app/src/TestApp.tsx"], "sourcesContent": ["import { useState } from \"react\";\nimport { Button } from \"./components/ui/button\";\nimport { Input } from \"./components/ui/input\";\nimport { Label } from \"./components/ui/label\";\n\n// مكون تسجيل الدخول البسيط\nfunction SimpleLoginScreen({ onLogin }: { onLogin: (success: boolean) => void }) {\n  const [subscriptionCode, setSubscriptionCode] = useState(\"\");\n  const [error, setError] = useState(\"\");\n  const [isLoading, setIsLoading] = useState(false);\n\n  const handleLogin = () => {\n    setIsLoading(true);\n    setError(\"\");\n    \n    // محاكاة التحقق من رمز الاشتراك\n    setTimeout(() => {\n      if (subscriptionCode === \"VIP123\") {\n        onLogin(true);\n      } else {\n        setError(\"رمز الاشتراك غير صحيح. جرب: VIP123\");\n        onLogin(false);\n      }\n      setIsLoading(false);\n    }, 1000);\n  };\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100\" dir=\"rtl\">\n      <div className=\"bg-white p-8 rounded-lg shadow-lg w-full max-w-md\">\n        <div className=\"text-center mb-6\">\n          <h1 className=\"text-3xl font-bold text-gray-800 mb-2\">☕ مقهى VIP</h1>\n          <p className=\"text-gray-600\">أدخل رمز الاشتراك للدخول</p>\n        </div>\n        \n        <div className=\"space-y-4\">\n          <div>\n            <Label htmlFor=\"subscription-code\" className=\"text-right block mb-2\">\n              رمز الاشتراك\n            </Label>\n            <Input\n              id=\"subscription-code\"\n              type=\"text\"\n              placeholder=\"أدخل رمز الاشتراك (مثال: VIP123)\"\n              value={subscriptionCode}\n              onChange={(e) => setSubscriptionCode(e.target.value)}\n              className=\"text-center text-lg\"\n              onKeyPress={(e) => e.key === 'Enter' && handleLogin()}\n            />\n          </div>\n          \n          {error && (\n            <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded text-center\">\n              {error}\n            </div>\n          )}\n          \n          <Button \n            onClick={handleLogin} \n            disabled={!subscriptionCode || isLoading}\n            className=\"w-full text-lg py-3\"\n          >\n            {isLoading ? \"جاري التحقق...\" : \"🔓 دخول\"}\n          </Button>\n          \n          <div className=\"text-center text-sm text-gray-500 mt-4\">\n            <p>💡 تلميح: استخدم الرمز <strong>VIP123</strong></p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\n// التطبيق الرئيسي المبسط\nexport default function TestApp() {\n  const [isLoggedIn, setIsLoggedIn] = useState(false);\n\n  const handleLogin = (success: boolean) => {\n    if (success) {\n      setIsLoggedIn(true);\n    }\n  };\n\n  const handleLogout = () => {\n    setIsLoggedIn(false);\n  };\n\n  // إذا لم يسجل الدخول، اعرض شاشة تسجيل الدخول\n  if (!isLoggedIn) {\n    return <SimpleLoginScreen onLogin={handleLogin} />;\n  }\n\n  // واجهة إدارة المقهى الرئيسية\n  return (\n    <div className=\"p-6 max-w-4xl mx-auto text-right\" dir=\"rtl\">\n      <div className=\"flex justify-between items-center mb-6\">\n        <h1 className=\"text-2xl font-bold\">🍽️ إدارة الطاولات - مقهى VIP</h1>\n        <Button \n          variant=\"outline\" \n          onClick={handleLogout}\n          className=\"text-red-600 hover:text-red-700\"\n        >\n          🚪 تسجيل خروج\n        </Button>\n      </div>\n      \n      <div className=\"bg-green-50 p-6 rounded-lg text-center\">\n        <h2 className=\"text-xl font-bold text-green-800 mb-2\">🎉 تم تسجيل الدخول بنجاح!</h2>\n        <p className=\"text-green-700\">مرحباً بك في نظام إدارة مقهى VIP</p>\n      </div>\n    </div>\n  );\n}\n"], "mappings": ";;;AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,MAAM,QAAQ,wBAAwB;AAC/C,SAASC,KAAK,QAAQ,uBAAuB;AAC7C,SAASC,KAAK,QAAQ,uBAAuB;;AAE7C;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,SAASC,iBAAiBA,CAAC;EAAEC;AAAiD,CAAC,EAAE;EAAAC,EAAA;EAC/E,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACW,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACa,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAMe,WAAW,GAAGA,CAAA,KAAM;IACxBD,YAAY,CAAC,IAAI,CAAC;IAClBF,QAAQ,CAAC,EAAE,CAAC;;IAEZ;IACAI,UAAU,CAAC,MAAM;MACf,IAAIP,gBAAgB,KAAK,QAAQ,EAAE;QACjCF,OAAO,CAAC,IAAI,CAAC;MACf,CAAC,MAAM;QACLK,QAAQ,CAAC,oCAAoC,CAAC;QAC9CL,OAAO,CAAC,KAAK,CAAC;MAChB;MACAO,YAAY,CAAC,KAAK,CAAC;IACrB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,oBACET,OAAA;IAAKY,SAAS,EAAC,4FAA4F;IAACC,GAAG,EAAC,KAAK;IAAAC,QAAA,eACnHd,OAAA;MAAKY,SAAS,EAAC,mDAAmD;MAAAE,QAAA,gBAChEd,OAAA;QAAKY,SAAS,EAAC,kBAAkB;QAAAE,QAAA,gBAC/Bd,OAAA;UAAIY,SAAS,EAAC,uCAAuC;UAAAE,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrElB,OAAA;UAAGY,SAAS,EAAC,eAAe;UAAAE,QAAA,EAAC;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC,eAENlB,OAAA;QAAKY,SAAS,EAAC,WAAW;QAAAE,QAAA,gBACxBd,OAAA;UAAAc,QAAA,gBACEd,OAAA,CAACF,KAAK;YAACqB,OAAO,EAAC,mBAAmB;YAACP,SAAS,EAAC,uBAAuB;YAAAE,QAAA,EAAC;UAErE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRlB,OAAA,CAACH,KAAK;YACJuB,EAAE,EAAC,mBAAmB;YACtBC,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,iIAAkC;YAC9CC,KAAK,EAAEnB,gBAAiB;YACxBoB,QAAQ,EAAGC,CAAC,IAAKpB,mBAAmB,CAACoB,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YACrDX,SAAS,EAAC,qBAAqB;YAC/Be,UAAU,EAAGF,CAAC,IAAKA,CAAC,CAACG,GAAG,KAAK,OAAO,IAAIlB,WAAW,CAAC;UAAE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAELZ,KAAK,iBACJN,OAAA;UAAKY,SAAS,EAAC,4EAA4E;UAAAE,QAAA,EACxFR;QAAK;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAEDlB,OAAA,CAACJ,MAAM;UACLiC,OAAO,EAAEnB,WAAY;UACrBoB,QAAQ,EAAE,CAAC1B,gBAAgB,IAAII,SAAU;UACzCI,SAAS,EAAC,qBAAqB;UAAAE,QAAA,EAE9BN,SAAS,GAAG,gBAAgB,GAAG;QAAS;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eAETlB,OAAA;UAAKY,SAAS,EAAC,wCAAwC;UAAAE,QAAA,eACrDd,OAAA;YAAAc,QAAA,GAAG,mHAAuB,eAAAd,OAAA;cAAAc,QAAA,EAAQ;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;;AAEA;AAAAf,EAAA,CApESF,iBAAiB;AAAA8B,EAAA,GAAjB9B,iBAAiB;AAqE1B,eAAe,SAAS+B,OAAOA,CAAA,EAAG;EAAAC,GAAA;EAChC,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EAEnD,MAAMe,WAAW,GAAI0B,OAAgB,IAAK;IACxC,IAAIA,OAAO,EAAE;MACXD,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC;EAED,MAAME,YAAY,GAAGA,CAAA,KAAM;IACzBF,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;;EAED;EACA,IAAI,CAACD,UAAU,EAAE;IACf,oBAAOlC,OAAA,CAACC,iBAAiB;MAACC,OAAO,EAAEQ;IAAY;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACpD;;EAEA;EACA,oBACElB,OAAA;IAAKY,SAAS,EAAC,kCAAkC;IAACC,GAAG,EAAC,KAAK;IAAAC,QAAA,gBACzDd,OAAA;MAAKY,SAAS,EAAC,wCAAwC;MAAAE,QAAA,gBACrDd,OAAA;QAAIY,SAAS,EAAC,oBAAoB;QAAAE,QAAA,EAAC;MAA6B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrElB,OAAA,CAACJ,MAAM;QACL0C,OAAO,EAAC,SAAS;QACjBT,OAAO,EAAEQ,YAAa;QACtBzB,SAAS,EAAC,iCAAiC;QAAAE,QAAA,EAC5C;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENlB,OAAA;MAAKY,SAAS,EAAC,wCAAwC;MAAAE,QAAA,gBACrDd,OAAA;QAAIY,SAAS,EAAC,uCAAuC;QAAAE,QAAA,EAAC;MAAyB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpFlB,OAAA;QAAGY,SAAS,EAAC,gBAAgB;QAAAE,QAAA,EAAC;MAAgC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/D,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACe,GAAA,CAtCuBD,OAAO;AAAAO,GAAA,GAAPP,OAAO;AAAA,IAAAD,EAAA,EAAAQ,GAAA;AAAAC,YAAA,CAAAT,EAAA;AAAAS,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}