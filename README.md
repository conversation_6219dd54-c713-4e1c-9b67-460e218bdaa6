# 🍽️ مقهى VIP - نظام إدارة الطاولات

## 🚀 الميزات الجديدة

### 🔐 نظام تسجيل الدخول
- **رمز الاشتراك**: `VIP123`
- واجهة تسجيل دخول أنيقة مع التحقق من الرمز
- إمكانية تسجيل الخروج

### 📋 قائمة الأصناف الكاملة
تم إضافة جميع الأصناف المطلوبة:

#### ☕ المشروبات الساخنة
- شاي (500 د.ع)
- شاي دارسين (750 د.ع)
- قهوة تركية (1000 د.ع)
- نسكافيه (1500 د.ع)
- كابتشينو (2000 د.ع)

#### 🥤 المشروبات الباردة
- ماء صغير (500 د.ع)
- ماء كبير (1000 د.ع)
- عصير برتقال طبيعي (2500 د.ع)
- سفن أب / بيبسي / ميرندا (1000 د.ع)

#### 🚬 النراكيل
- نركيلة تفاحتين (4000 د.ع)
- نركيلة عنب نعناع (4000 د.ع)
- نركيلة بطيخ نعناع (4000 د.ع)
- نركيلة فخفخينا (4000 د.ع)

#### 🍽️ الطعام
- بطاطا مقلية (2000 د.ع)
- ساندويتش شاورما (3000 د.ع)

#### ➕ إضافات النركيلة
- معسل إضافي (2000 د.ع)
- راس جديد (1000 د.ع)

### 🎯 الميزات المتقدمة
- **📊 إحصائيات مباشرة**: عرض إجمالي الأصناف والمتوفر والفئات
- **🔍 فلترة ذكية**: فلترة الأصناف حسب الفئة مع عداد لكل فئة
- **🎨 واجهة محسنة**: رموز تعبيرية وألوان مميزة لكل فئة
- **⚡ تبديل سريع**: تفعيل/إلغاء تفعيل الأصناف بنقرة واحدة
- **💰 عرض الأسعار**: تنسيق الأسعار بالدينار العراقي

## 🛠️ كيفية التشغيل

```bash
# تثبيت التبعيات
npm install

# تشغيل التطبيق
npm start
```

## 🔑 بيانات الدخول
- **رمز الاشتراك**: `VIP123`

## 📱 كيفية الاستخدام

1. **تسجيل الدخول**: أدخل رمز الاشتراك `VIP123`
2. **عرض الإحصائيات**: شاهد إجمالي الأصناف والمتوفر
3. **فلترة الأصناف**: اختر فئة معينة لعرض أصنافها فقط
4. **إدارة التوفر**: استخدم المفتاح لتفعيل/إلغاء تفعيل الأصناف
5. **إضافة أصناف جديدة**: استخدم زر "إضافة صنف جديد"
6. **حذف الأصناف**: استخدم زر الحذف لإزالة الأصناف غير المرغوبة

## 🎨 التصميم
- تصميم متجاوب يعمل على جميع الأجهزة
- واجهة باللغة العربية مع دعم RTL
- ألوان مميزة لكل فئة
- رموز تعبيرية واضحة

## 🔧 التقنيات المستخدمة
- React 18
- TypeScript
- Tailwind CSS
- Shadcn/ui Components
