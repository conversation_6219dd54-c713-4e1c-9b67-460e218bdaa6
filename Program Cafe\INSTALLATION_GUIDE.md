# 🍽️ دليل التشغيل التفصيلي - برنامج مقهى VIP

## 📋 نظرة عامة
برنامج إدارة مقهى متطور مع نظام تسجيل دخول وإدارة شاملة للأصناف والفئات.

---

## 🔧 متطلبات النظام

### المتطلبات الأساسية:
- **Node.js** (الإصدار 16 أو أحدث)
- **npm** (يأتي مع Node.js)
- **متصفح ويب حديث** (Chrome, Firefox, Edge, Safari)
- **نظام التشغيل**: Windows, macOS, أو Linux

### تحميل Node.js:
1. اذهب إلى: https://nodejs.org
2. حمل النسخة LTS (الموصى بها)
3. قم بالتثبيت واتبع التعليمات

---

## 🚀 خطوات التشغيل

### الخطوة 1: فتح الطرفية (Terminal)
**في Windows:**
- اضغط `Win + R`
- اكتب `cmd` أو `powershell`
- اضغط Enter

**في macOS:**
- اضغط `Cmd + Space`
- اكتب `Terminal`
- اضغط Enter

**في Linux:**
- اضغط `Ctrl + Alt + T`

### الخطوة 2: الانتقال إلى مجلد البرنامج
```bash
cd "D:\Program Cafe"
```

### الخطوة 3: تثبيت التبعيات
```bash
npm install
```
⏱️ **الوقت المتوقع**: 2-5 دقائق

### الخطوة 4: تشغيل البرنامج
```bash
npm start
```

### الخطوة 5: فتح المتصفح
- سيفتح البرنامج تلقائياً على: `http://localhost:3000`
- إذا لم يفتح تلقائياً، افتح المتصفح واذهب إلى العنوان أعلاه

---

## 🔑 بيانات الدخول

### رمز الاشتراك:
```
VIP123
```

**ملاحظة**: الرمز حساس لحالة الأحرف (كبيرة وصغيرة)

---

## 📱 كيفية الاستخدام

### 1. تسجيل الدخول
- أدخل رمز الاشتراك: `VIP123`
- اضغط زر "دخول" أو اضغط Enter

### 2. الواجهة الرئيسية
بعد تسجيل الدخول ستجد:

#### 📊 لوحة الإحصائيات:
- **إجمالي الأصناف**: عدد جميع الأصناف
- **المتوفر**: عدد الأصناف المتاحة
- **الفئات**: عدد فئات الأصناف

#### 🔍 فلترة الأصناف:
- **الكل**: عرض جميع الأصناف
- **☕ مشروب ساخن**: الشاي والقهوة
- **🥤 مشروب بارد**: العصائر والمياه
- **🚬 نركيلة**: جميع أنواع النراكيل
- **🍽️ طعام**: الأطعمة المختلفة
- **➕ إضافات نركيلة**: المعسل والرؤوس

#### 📋 جدول الأصناف:
- **اسم الصنف**: اسم المنتج
- **الفئة**: نوع المنتج مع رمز تعبيري
- **السعر**: بالدينار العراقي
- **التوفر**: مفتاح تفعيل/إلغاء
- **الإجراءات**: زر الحذف

### 3. إدارة الأصناف

#### إضافة صنف جديد:
1. اضغط "إضافة صنف جديد"
2. املأ البيانات:
   - **اسم الصنف**: مثل "شاي بالنعناع"
   - **الفئة**: اختر من القائمة
   - **السعر**: بالدينار العراقي
3. اضغط "إضافة"

#### تعديل التوفر:
- استخدم المفتاح بجانب كل صنف
- ✅ متوفر / ❌ غير متوفر

#### حذف صنف:
- اضغط زر "🗑️ حذف" بجانب الصنف

### 4. تسجيل الخروج
- اضغط زر "🚪 تسجيل خروج" في أعلى الصفحة

---

## 📦 الأصناف المتاحة (17 صنف)

### ☕ المشروبات الساخنة (5 أصناف):
- شاي (500 د.ع)
- شاي دارسين (750 د.ع)
- قهوة تركية (1000 د.ع)
- نسكافيه (1500 د.ع)
- كابتشينو (2000 د.ع)

### 🥤 المشروبات الباردة (4 أصناف):
- ماء صغير (500 د.ع)
- ماء كبير (1000 د.ع)
- عصير برتقال طبيعي (2500 د.ع)
- سفن أب / بيبسي / ميرندا (1000 د.ع)

### 🚬 النراكيل (4 أصناف):
- نركيلة تفاحتين (4000 د.ع)
- نركيلة عنب نعناع (4000 د.ع)
- نركيلة بطيخ نعناع (4000 د.ع)
- نركيلة فخفخينا (4000 د.ع)

### 🍽️ الطعام (2 صنف):
- بطاطا مقلية (2000 د.ع)
- ساندويتش شاورما (3000 د.ع)

### ➕ إضافات النركيلة (2 صنف):
- معسل إضافي (2000 د.ع)
- راس جديد (1000 د.ع)

---

## 🛠️ حل المشاكل الشائعة

### المشكلة: "npm غير معروف"
**الحل:**
1. تأكد من تثبيت Node.js
2. أعد تشغيل الطرفية
3. جرب: `node --version`

### المشكلة: "خطأ في التبعيات"
**الحل:**
```bash
npm cache clean --force
npm install
```

### المشكلة: "المنفذ مستخدم"
**الحل:**
```bash
# إيقاف العملية الحالية
Ctrl + C

# تشغيل على منفذ مختلف
npm start -- --port 3001
```

### المشكلة: "الصفحة لا تفتح"
**الحل:**
1. تأكد من تشغيل الأمر `npm start`
2. انتظر رسالة "compiled successfully"
3. جرب العنوان: `http://localhost:3000`

---

## 📞 الدعم التقني

### ملفات مهمة:
- **package.json**: إعدادات المشروع
- **src/App.tsx**: الكود الرئيسي
- **src/components/**: مكونات الواجهة
- **public/index.html**: صفحة HTML الأساسية

### أوامر مفيدة:
```bash
# تشغيل البرنامج
npm start

# بناء نسخة الإنتاج
npm run build

# فحص الأخطاء
npm run lint

# تشغيل الاختبارات
npm test
```

---

## 🎯 ميزات متقدمة

### 1. التصميم المتجاوب
- يعمل على جميع أحجام الشاشات
- متوافق مع الهواتف والأجهزة اللوحية

### 2. دعم اللغة العربية
- واجهة كاملة باللغة العربية
- دعم الكتابة من اليمين إلى اليسار (RTL)

### 3. الحفظ التلقائي
- يحفظ التغييرات تلقائياً في المتصفح
- لا يحتاج قاعدة بيانات خارجية

### 4. الأمان
- نظام تسجيل دخول محمي
- التحقق من صحة البيانات

---

## 📝 ملاحظات مهمة

1. **النسخ الاحتياطي**: احفظ نسخة من المجلد كاملاً
2. **التحديثات**: تحقق من التحديثات دورياً
3. **الأداء**: أغلق التطبيقات الأخرى لأداء أفضل
4. **الشبكة**: يمكن الوصول للبرنامج من أجهزة أخرى على نفس الشبكة

---

## ✅ تم إنشاء البرنامج بنجاح!

**تاريخ الإنشاء**: يوليو 2025  
**الإصدار**: 1.0.0  
**المطور**: Augment Agent  

🎉 **مبروك! برنامجك جاهز للاستخدام**
