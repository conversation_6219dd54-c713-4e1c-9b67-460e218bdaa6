{"ast": null, "code": "var _jsxFileName = \"D:\\\\hazimpro\\\\cafe-app\\\\src\\\\components\\\\ui\\\\table.tsx\";\nimport * as React from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport function Table({\n  children\n}) {\n  return /*#__PURE__*/_jsxDEV(\"table\", {\n    className: \"min-w-full divide-y divide-gray-200\",\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 4,\n    columnNumber: 10\n  }, this);\n}\n_c = Table;\nexport function TableHeader({\n  children\n}) {\n  return /*#__PURE__*/_jsxDEV(\"thead\", {\n    className: \"bg-gray-50\",\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 10\n  }, this);\n}\n_c2 = TableHeader;\nexport function TableBody({\n  children\n}) {\n  return /*#__PURE__*/_jsxDEV(\"tbody\", {\n    className: \"bg-white divide-y divide-gray-200\",\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 10\n  }, this);\n}\n_c3 = TableBody;\nexport function TableRow({\n  children\n}) {\n  return /*#__PURE__*/_jsxDEV(\"tr\", {\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 10\n  }, this);\n}\n_c4 = TableRow;\nexport function TableHead({\n  children\n}) {\n  return /*#__PURE__*/_jsxDEV(\"th\", {\n    scope: \"col\",\n    className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 10\n  }, this);\n}\n_c5 = TableHead;\nexport function TableCell({\n  children,\n  className = \"\",\n  colSpan\n}) {\n  return /*#__PURE__*/_jsxDEV(\"td\", {\n    className: `px-6 py-4 whitespace-nowrap text-sm text-gray-700 ${className}`,\n    colSpan: colSpan,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 33,\n    columnNumber: 5\n  }, this);\n}\n_c6 = TableCell;\nvar _c, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"Table\");\n$RefreshReg$(_c2, \"TableHeader\");\n$RefreshReg$(_c3, \"TableBody\");\n$RefreshReg$(_c4, \"TableRow\");\n$RefreshReg$(_c5, \"TableHead\");\n$RefreshReg$(_c6, \"TableCell\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Table", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "TableHeader", "_c2", "TableBody", "_c3", "TableRow", "_c4", "TableHead", "scope", "_c5", "TableCell", "colSpan", "_c6", "$RefreshReg$"], "sources": ["D:/hazimpro/cafe-app/src/components/ui/table.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nexport function Table({ children }: { children: React.ReactNode }) {\n  return <table className=\"min-w-full divide-y divide-gray-200\">{children}</table>\n}\n\nexport function TableHeader({ children }: { children: React.ReactNode }) {\n  return <thead className=\"bg-gray-50\">{children}</thead>\n}\n\nexport function TableBody({ children }: { children: React.ReactNode }) {\n  return <tbody className=\"bg-white divide-y divide-gray-200\">{children}</tbody>\n}\n\nexport function TableRow({ children }: { children: React.ReactNode }) {\n  return <tr>{children}</tr>\n}\n\nexport function TableHead({ children }: { children: React.ReactNode }) {\n  return <th scope=\"col\" className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">{children}</th>\n}\n\nexport function TableCell({\n  children,\n  className = \"\",\n  colSpan\n}: {\n  children: React.ReactNode;\n  className?: string;\n  colSpan?: number;\n}) {\n  return (\n    <td\n      className={`px-6 py-4 whitespace-nowrap text-sm text-gray-700 ${className}`}\n      colSpan={colSpan}\n    >\n      {children}\n    </td>\n  );\n}\n"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAE9B,OAAO,SAASC,KAAKA,CAAC;EAAEC;AAAwC,CAAC,EAAE;EACjE,oBAAOF,OAAA;IAAOG,SAAS,EAAC,qCAAqC;IAAAD,QAAA,EAAEA;EAAQ;IAAAE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAQ,CAAC;AAClF;AAACC,EAAA,GAFeP,KAAK;AAIrB,OAAO,SAASQ,WAAWA,CAAC;EAAEP;AAAwC,CAAC,EAAE;EACvE,oBAAOF,OAAA;IAAOG,SAAS,EAAC,YAAY;IAAAD,QAAA,EAAEA;EAAQ;IAAAE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAQ,CAAC;AACzD;AAACG,GAAA,GAFeD,WAAW;AAI3B,OAAO,SAASE,SAASA,CAAC;EAAET;AAAwC,CAAC,EAAE;EACrE,oBAAOF,OAAA;IAAOG,SAAS,EAAC,mCAAmC;IAAAD,QAAA,EAAEA;EAAQ;IAAAE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAQ,CAAC;AAChF;AAACK,GAAA,GAFeD,SAAS;AAIzB,OAAO,SAASE,QAAQA,CAAC;EAAEX;AAAwC,CAAC,EAAE;EACpE,oBAAOF,OAAA;IAAAE,QAAA,EAAKA;EAAQ;IAAAE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;AAC5B;AAACO,GAAA,GAFeD,QAAQ;AAIxB,OAAO,SAASE,SAASA,CAAC;EAAEb;AAAwC,CAAC,EAAE;EACrE,oBAAOF,OAAA;IAAIgB,KAAK,EAAC,KAAK;IAACb,SAAS,EAAC,iFAAiF;IAAAD,QAAA,EAAEA;EAAQ;IAAAE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;AACpI;AAACU,GAAA,GAFeF,SAAS;AAIzB,OAAO,SAASG,SAASA,CAAC;EACxBhB,QAAQ;EACRC,SAAS,GAAG,EAAE;EACdgB;AAKF,CAAC,EAAE;EACD,oBACEnB,OAAA;IACEG,SAAS,EAAE,qDAAqDA,SAAS,EAAG;IAC5EgB,OAAO,EAAEA,OAAQ;IAAAjB,QAAA,EAEhBA;EAAQ;IAAAE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAET;AAACa,GAAA,GAjBeF,SAAS;AAAA,IAAAV,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA;AAAAC,YAAA,CAAAb,EAAA;AAAAa,YAAA,CAAAX,GAAA;AAAAW,YAAA,CAAAT,GAAA;AAAAS,YAAA,CAAAP,GAAA;AAAAO,YAAA,CAAAJ,GAAA;AAAAI,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}