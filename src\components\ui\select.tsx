import * as React from "react"

interface SelectContextType {
  value: string;
  onValueChange: (value: string) => void;
  open: boolean;
  setOpen: (open: boolean) => void;
}

const SelectContext = React.createContext<SelectContextType | null>(null);

export function Select({ onValueChange, children }: { onValueChange: (value: string) => void, children: React.ReactNode }) {
  const [value, setValue] = React.useState("");
  const [open, setOpen] = React.useState(false);

  const handleValueChange = (newValue: string) => {
    setValue(newValue);
    onValueChange(newValue);
    setOpen(false);
  };

  return (
    <SelectContext.Provider value={{ value, onValueChange: handleValueChange, open, setOpen }}>
      <div className="relative">
        {children}
      </div>
    </SelectContext.Provider>
  );
}

export function SelectTrigger({ children }: { children: React.ReactNode }) {
  const context = React.useContext(SelectContext);

  return (
    <div
      className="border p-3 rounded cursor-pointer flex justify-between items-center bg-white hover:bg-gray-50"
      onClick={() => context?.setOpen(!context.open)}
    >
      {children}
      <span className="text-gray-400">▼</span>
    </div>
  );
}

export function SelectValue({ placeholder }: { placeholder: string }) {
  const context = React.useContext(SelectContext);

  return (
    <span className={context?.value ? "text-black" : "text-gray-500"}>
      {context?.value || placeholder}
    </span>
  );
}

export function SelectContent({ children }: { children: React.ReactNode }) {
  const context = React.useContext(SelectContext);

  if (!context?.open) return null;

  return (
    <div className="absolute top-full left-0 right-0 border mt-1 rounded bg-white shadow-lg z-50 max-h-60 overflow-y-auto">
      {children}
    </div>
  );
}

export function SelectItem({ value, children }: { value: string, children: React.ReactNode }) {
  const context = React.useContext(SelectContext);

  return (
    <div
      className="cursor-pointer hover:bg-gray-100 p-3 text-right"
      onClick={() => context?.onValueChange(value)}
    >
      {children}
    </div>
  );
}
