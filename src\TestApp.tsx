import { useState } from "react";
import { Button } from "./components/ui/button";
import { Input } from "./components/ui/input";
import { Label } from "./components/ui/label";

// مكون تسجيل الدخول البسيط
function SimpleLoginScreen({ onLogin }: { onLogin: (success: boolean) => void }) {
  const [subscriptionCode, setSubscriptionCode] = useState("");
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const handleLogin = () => {
    setIsLoading(true);
    setError("");
    
    // محاكاة التحقق من رمز الاشتراك
    setTimeout(() => {
      if (subscriptionCode === "VIP123") {
        onLogin(true);
      } else {
        setError("رمز الاشتراك غير صحيح. جرب: VIP123");
        onLogin(false);
      }
      setIsLoading(false);
    }, 1000);
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100" dir="rtl">
      <div className="bg-white p-8 rounded-lg shadow-lg w-full max-w-md">
        <div className="text-center mb-6">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">☕ مقهى VIP</h1>
          <p className="text-gray-600">أدخل رمز الاشتراك للدخول</p>
        </div>
        
        <div className="space-y-4">
          <div>
            <Label htmlFor="subscription-code" className="text-right block mb-2">
              رمز الاشتراك
            </Label>
            <Input
              id="subscription-code"
              type="text"
              placeholder="أدخل رمز الاشتراك (مثال: VIP123)"
              value={subscriptionCode}
              onChange={(e) => setSubscriptionCode(e.target.value)}
              className="text-center text-lg"
              onKeyPress={(e) => e.key === 'Enter' && handleLogin()}
            />
          </div>
          
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded text-center">
              {error}
            </div>
          )}
          
          <Button 
            onClick={handleLogin} 
            disabled={!subscriptionCode || isLoading}
            className="w-full text-lg py-3"
          >
            {isLoading ? "جاري التحقق..." : "🔓 دخول"}
          </Button>
          
          <div className="text-center text-sm text-gray-500 mt-4">
            <p>💡 تلميح: استخدم الرمز <strong>VIP123</strong></p>
          </div>
        </div>
      </div>
    </div>
  );
}

// التطبيق الرئيسي المبسط
export default function TestApp() {
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  const handleLogin = (success: boolean) => {
    if (success) {
      setIsLoggedIn(true);
    }
  };

  const handleLogout = () => {
    setIsLoggedIn(false);
  };

  // إذا لم يسجل الدخول، اعرض شاشة تسجيل الدخول
  if (!isLoggedIn) {
    return <SimpleLoginScreen onLogin={handleLogin} />;
  }

  // واجهة إدارة المقهى الرئيسية
  return (
    <div className="p-6 max-w-4xl mx-auto text-right" dir="rtl">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">🍽️ إدارة الطاولات - مقهى VIP</h1>
        <Button 
          variant="outline" 
          onClick={handleLogout}
          className="text-red-600 hover:text-red-700"
        >
          🚪 تسجيل خروج
        </Button>
      </div>
      
      <div className="bg-green-50 p-6 rounded-lg text-center">
        <h2 className="text-xl font-bold text-green-800 mb-2">🎉 تم تسجيل الدخول بنجاح!</h2>
        <p className="text-green-700">مرحباً بك في نظام إدارة مقهى VIP</p>
      </div>
    </div>
  );
}
