{"ast": null, "code": "var _jsxFileName = \"D:\\\\hazimpro\\\\cafe-app\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport * as React from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DialogContext = /*#__PURE__*/React.createContext(null);\nexport function Dialog({\n  children\n}) {\n  _s();\n  const [open, setOpen] = React.useState(false);\n  return /*#__PURE__*/_jsxDEV(DialogContext.Provider, {\n    value: {\n      open,\n      setOpen\n    },\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 5\n  }, this);\n}\n_s(Dialog, \"xG1TONbKtDWtdOTrXaTAsNhPg/Q=\");\n_c = Dialog;\nexport function DialogTrigger({\n  children\n}) {\n  _s2();\n  const context = React.useContext(DialogContext);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    onClick: () => context === null || context === void 0 ? void 0 : context.setOpen(true),\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 24,\n    columnNumber: 5\n  }, this);\n}\n_s2(DialogTrigger, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n_c2 = DialogTrigger;\nexport function DialogContent({\n  children,\n  dir\n}) {\n  _s3();\n  const context = React.useContext(DialogContext);\n  if (!(context !== null && context !== void 0 && context.open)) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 p-4 z-50\",\n    onClick: () => context.setOpen(false),\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      dir: dir,\n      className: \"bg-white p-6 rounded-lg shadow-lg w-full max-w-md\",\n      onClick: e => e.stopPropagation(),\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => context.setOpen(false),\n        className: \"absolute top-2 right-2 text-gray-500 hover:text-gray-700\",\n        children: \"\\u2715\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this), children]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 36,\n    columnNumber: 5\n  }, this);\n}\n_s3(DialogContent, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n_c3 = DialogContent;\nexport function DialogTitle({\n  children\n}) {\n  return /*#__PURE__*/_jsxDEV(\"h2\", {\n    className: \"text-lg font-semibold mb-4 text-right\",\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 58,\n    columnNumber: 10\n  }, this);\n}\n_c4 = DialogTitle;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"Dialog\");\n$RefreshReg$(_c2, \"DialogTrigger\");\n$RefreshReg$(_c3, \"DialogContent\");\n$RefreshReg$(_c4, \"DialogTitle\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "DialogContext", "createContext", "Dialog", "children", "_s", "open", "<PERSON><PERSON><PERSON>", "useState", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "DialogTrigger", "_s2", "context", "useContext", "onClick", "_c2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dir", "_s3", "className", "e", "stopPropagation", "_c3", "DialogTitle", "_c4", "$RefreshReg$"], "sources": ["D:/hazimpro/cafe-app/src/components/ui/dialog.tsx"], "sourcesContent": ["import * as React from \"react\"\n\ninterface DialogContextType {\n  open: boolean;\n  setOpen: (open: boolean) => void;\n}\n\nconst DialogContext = React.createContext<DialogContextType | null>(null);\n\nexport function Dialog({ children }: { children: React.ReactNode }) {\n  const [open, setOpen] = React.useState(false);\n\n  return (\n    <DialogContext.Provider value={{ open, setOpen }}>\n      {children}\n    </DialogContext.Provider>\n  );\n}\n\nexport function DialogTrigger({ children }: { children: React.ReactNode }) {\n  const context = React.useContext(DialogContext);\n\n  return (\n    <div onClick={() => context?.setOpen(true)}>\n      {children}\n    </div>\n  );\n}\n\nexport function DialogContent({ children, dir }: { children: React.ReactNode, dir?: string }) {\n  const context = React.useContext(DialogContext);\n\n  if (!context?.open) return null;\n\n  return (\n    <div\n      className=\"fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 p-4 z-50\"\n      onClick={() => context.setOpen(false)}\n    >\n      <div\n        dir={dir}\n        className=\"bg-white p-6 rounded-lg shadow-lg w-full max-w-md\"\n        onClick={(e) => e.stopPropagation()}\n      >\n        <button\n          onClick={() => context.setOpen(false)}\n          className=\"absolute top-2 right-2 text-gray-500 hover:text-gray-700\"\n        >\n          ✕\n        </button>\n        {children}\n      </div>\n    </div>\n  );\n}\n\nexport function DialogTitle({ children }: { children: React.ReactNode }) {\n  return <h2 className=\"text-lg font-semibold mb-4 text-right\">{children}</h2>;\n}\n"], "mappings": ";;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAO9B,MAAMC,aAAa,gBAAGH,KAAK,CAACI,aAAa,CAA2B,IAAI,CAAC;AAEzE,OAAO,SAASC,MAAMA,CAAC;EAAEC;AAAwC,CAAC,EAAE;EAAAC,EAAA;EAClE,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGT,KAAK,CAACU,QAAQ,CAAC,KAAK,CAAC;EAE7C,oBACER,OAAA,CAACC,aAAa,CAACQ,QAAQ;IAACC,KAAK,EAAE;MAAEJ,IAAI;MAAEC;IAAQ,CAAE;IAAAH,QAAA,EAC9CA;EAAQ;IAAAO,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACa,CAAC;AAE7B;AAACT,EAAA,CAReF,MAAM;AAAAY,EAAA,GAANZ,MAAM;AAUtB,OAAO,SAASa,aAAaA,CAAC;EAAEZ;AAAwC,CAAC,EAAE;EAAAa,GAAA;EACzE,MAAMC,OAAO,GAAGpB,KAAK,CAACqB,UAAU,CAAClB,aAAa,CAAC;EAE/C,oBACED,OAAA;IAAKoB,OAAO,EAAEA,CAAA,KAAMF,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEX,OAAO,CAAC,IAAI,CAAE;IAAAH,QAAA,EACxCA;EAAQ;IAAAO,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV;AAACG,GAAA,CAReD,aAAa;AAAAK,GAAA,GAAbL,aAAa;AAU7B,OAAO,SAASM,aAAaA,CAAC;EAAElB,QAAQ;EAAEmB;AAAiD,CAAC,EAAE;EAAAC,GAAA;EAC5F,MAAMN,OAAO,GAAGpB,KAAK,CAACqB,UAAU,CAAClB,aAAa,CAAC;EAE/C,IAAI,EAACiB,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEZ,IAAI,GAAE,OAAO,IAAI;EAE/B,oBACEN,OAAA;IACEyB,SAAS,EAAC,gFAAgF;IAC1FL,OAAO,EAAEA,CAAA,KAAMF,OAAO,CAACX,OAAO,CAAC,KAAK,CAAE;IAAAH,QAAA,eAEtCJ,OAAA;MACEuB,GAAG,EAAEA,GAAI;MACTE,SAAS,EAAC,mDAAmD;MAC7DL,OAAO,EAAGM,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC,CAAE;MAAAvB,QAAA,gBAEpCJ,OAAA;QACEoB,OAAO,EAAEA,CAAA,KAAMF,OAAO,CAACX,OAAO,CAAC,KAAK,CAAE;QACtCkB,SAAS,EAAC,0DAA0D;QAAArB,QAAA,EACrE;MAED;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EACRV,QAAQ;IAAA;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACU,GAAA,CAzBeF,aAAa;AAAAM,GAAA,GAAbN,aAAa;AA2B7B,OAAO,SAASO,WAAWA,CAAC;EAAEzB;AAAwC,CAAC,EAAE;EACvE,oBAAOJ,OAAA;IAAIyB,SAAS,EAAC,uCAAuC;IAAArB,QAAA,EAAEA;EAAQ;IAAAO,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;AAC9E;AAACgB,GAAA,GAFeD,WAAW;AAAA,IAAAd,EAAA,EAAAM,GAAA,EAAAO,GAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAhB,EAAA;AAAAgB,YAAA,CAAAV,GAAA;AAAAU,YAAA,CAAAH,GAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}