<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مقهى VIP - تسجيل الدخول</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
    </style>
</head>
<body class="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100" dir="rtl">
    <div class="bg-white p-8 rounded-lg shadow-lg w-full max-w-md">
        <div class="text-center mb-6">
            <h1 class="text-3xl font-bold text-gray-800 mb-2">☕ مقهى VIP</h1>
            <p class="text-gray-600">أدخل رمز الاشتراك للدخول</p>
        </div>
        
        <div class="space-y-4">
            <div>
                <label for="subscription-code" class="text-right block mb-2 font-medium">
                    رمز الاشتراك
                </label>
                <input
                    id="subscription-code"
                    type="text"
                    placeholder="أدخل رمز الاشتراك (مثال: VIP123)"
                    class="w-full p-3 border border-gray-300 rounded-lg text-center text-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    onkeypress="if(event.key==='Enter') handleLogin()"
                />
            </div>
            
            <div id="error-message" class="hidden bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded text-center">
            </div>
            
            <button 
                onclick="handleLogin()" 
                class="w-full bg-blue-600 text-white text-lg py-3 rounded-lg hover:bg-blue-700 transition-colors"
                id="login-button"
            >
                🔓 دخول
            </button>
            
            <div class="text-center text-sm text-gray-500 mt-4">
                <p>💡 تلميح: استخدم الرمز <strong>VIP123</strong></p>
            </div>
        </div>
    </div>

    <!-- واجهة إدارة المقهى (مخفية في البداية) -->
    <div id="main-app" class="hidden p-6 max-w-4xl mx-auto text-right w-full">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold">🍽️ إدارة الطاولات - مقهى VIP</h1>
            <button 
                onclick="handleLogout()"
                class="px-4 py-2 border border-red-600 text-red-600 rounded hover:bg-red-50 transition-colors"
            >
                🚪 تسجيل خروج
            </button>
        </div>
        
        <div class="bg-green-50 p-6 rounded-lg text-center">
            <h2 class="text-xl font-bold text-green-800 mb-2">🎉 تم تسجيل الدخول بنجاح!</h2>
            <p class="text-green-700">مرحباً بك في نظام إدارة مقهى VIP</p>
            <p class="text-green-600 mt-2">جميع الأصناف والفئات متاحة الآن</p>
        </div>
    </div>

    <script>
        function handleLogin() {
            const input = document.getElementById('subscription-code');
            const errorDiv = document.getElementById('error-message');
            const button = document.getElementById('login-button');
            const loginScreen = document.querySelector('.bg-white');
            const mainApp = document.getElementById('main-app');
            
            const code = input.value.trim();
            
            if (!code) {
                showError('يرجى إدخال رمز الاشتراك');
                return;
            }
            
            // تغيير النص إلى "جاري التحقق..."
            button.textContent = 'جاري التحقق...';
            button.disabled = true;
            
            // محاكاة التحقق من الرمز
            setTimeout(() => {
                if (code === 'VIP123') {
                    // إخفاء شاشة تسجيل الدخول وإظهار التطبيق الرئيسي
                    loginScreen.style.display = 'none';
                    mainApp.classList.remove('hidden');
                    document.body.className = 'bg-gray-50';
                } else {
                    showError('رمز الاشتراك غير صحيح. جرب: VIP123');
                    button.textContent = '🔓 دخول';
                    button.disabled = false;
                }
            }, 1000);
        }
        
        function handleLogout() {
            const loginScreen = document.querySelector('.bg-white');
            const mainApp = document.getElementById('main-app');
            const input = document.getElementById('subscription-code');
            const button = document.getElementById('login-button');
            
            // إعادة تعيين الواجهة
            mainApp.classList.add('hidden');
            loginScreen.style.display = 'block';
            document.body.className = 'min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100';
            
            // مسح البيانات
            input.value = '';
            button.textContent = '🔓 دخول';
            button.disabled = false;
            hideError();
        }
        
        function showError(message) {
            const errorDiv = document.getElementById('error-message');
            errorDiv.textContent = message;
            errorDiv.classList.remove('hidden');
        }
        
        function hideError() {
            const errorDiv = document.getElementById('error-message');
            errorDiv.classList.add('hidden');
        }
    </script>
</body>
</html>
