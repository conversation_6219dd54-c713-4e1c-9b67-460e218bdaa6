[{"D:\\hazimpro\\cafe-app\\src\\index.tsx": "1", "D:\\hazimpro\\cafe-app\\src\\App.tsx": "2", "D:\\hazimpro\\cafe-app\\src\\components\\ui\\input.tsx": "3", "D:\\hazimpro\\cafe-app\\src\\components\\ui\\switch.tsx": "4", "D:\\hazimpro\\cafe-app\\src\\components\\ui\\table.tsx": "5", "D:\\hazimpro\\cafe-app\\src\\components\\ui\\select.tsx": "6", "D:\\hazimpro\\cafe-app\\src\\components\\ui\\button.tsx": "7", "D:\\hazimpro\\cafe-app\\src\\components\\ui\\dialog.tsx": "8", "D:\\hazimpro\\cafe-app\\src\\components\\ui\\label.tsx": "9", "D:\\hazimpro\\cafe-app\\src\\lib\\utils.ts": "10", "D:\\hazimpro\\cafe-app\\src\\TestApp.tsx": "11"}, {"size": 273, "mtime": 1752325272929, "results": "12", "hashOfConfig": "13"}, {"size": 13497, "mtime": 1752325209517, "results": "14", "hashOfConfig": "13"}, {"size": 640, "mtime": 1751147464454, "results": "15", "hashOfConfig": "13"}, {"size": 711, "mtime": 1751135454000, "results": "16", "hashOfConfig": "13"}, {"size": 1108, "mtime": 1752325230369, "results": "17", "hashOfConfig": "13"}, {"size": 2150, "mtime": 1752321550875, "results": "18", "hashOfConfig": "13"}, {"size": 1224, "mtime": 1751147451921, "results": "19", "hashOfConfig": "13"}, {"size": 1558, "mtime": 1752321514058, "results": "20", "hashOfConfig": "13"}, {"size": 425, "mtime": 1751147476190, "results": "21", "hashOfConfig": "13"}, {"size": 166, "mtime": 1751147367727, "results": "22", "hashOfConfig": "13"}, {"size": 3898, "mtime": 1752321732386, "results": "23", "hashOfConfig": "13"}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "12ozlh0", {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\hazimpro\\cafe-app\\src\\index.tsx", [], [], "D:\\hazimpro\\cafe-app\\src\\App.tsx", [], [], "D:\\hazimpro\\cafe-app\\src\\components\\ui\\input.tsx", [], [], "D:\\hazimpro\\cafe-app\\src\\components\\ui\\switch.tsx", [], [], "D:\\hazimpro\\cafe-app\\src\\components\\ui\\table.tsx", [], [], "D:\\hazimpro\\cafe-app\\src\\components\\ui\\select.tsx", [], [], "D:\\hazimpro\\cafe-app\\src\\components\\ui\\button.tsx", [], [], "D:\\hazimpro\\cafe-app\\src\\components\\ui\\dialog.tsx", [], [], "D:\\hazimpro\\cafe-app\\src\\components\\ui\\label.tsx", [], [], "D:\\hazimpro\\cafe-app\\src\\lib\\utils.ts", [], [], "D:\\hazimpro\\cafe-app\\src\\TestApp.tsx", [], []]