import { useState } from "react";
import { But<PERSON> } from "./components/ui/button";
import { Dialog, DialogContent, DialogTitle, DialogTrigger } from "./components/ui/dialog";
import { Input } from "./components/ui/input";
import { Label } from "./components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "./components/ui/table";
import { Switch } from "./components/ui/switch";

// مكون تسجيل الدخول
function LoginScreen({ onLogin }: { onLogin: (success: boolean) => void }) {
  const [subscriptionCode, setSubscriptionCode] = useState("");
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const handleLogin = () => {
    setIsLoading(true);
    setError("");

    // محاكاة التحقق من رمز الاشتراك
    setTimeout(() => {
      if (subscriptionCode === "VIP123") {
        onLogin(true);
      } else {
        setError("رمز الاشتراك غير صحيح. جرب: VIP123");
        onLogin(false);
      }
      setIsLoading(false);
    }, 1000);
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100" dir="rtl">
      <div className="bg-white p-8 rounded-lg shadow-lg w-full max-w-md">
        <div className="text-center mb-6">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">☕ مقهى VIP</h1>
          <p className="text-gray-600">أدخل رمز الاشتراك للدخول</p>
        </div>

        <div className="space-y-4">
          <div>
            <Label htmlFor="subscription-code" className="text-right block mb-2">
              رمز الاشتراك
            </Label>
            <Input
              id="subscription-code"
              type="text"
              placeholder="أدخل رمز الاشتراك (مثال: VIP123)"
              value={subscriptionCode}
              onChange={(e) => setSubscriptionCode(e.target.value)}
              className="text-center text-lg"
              onKeyPress={(e) => e.key === 'Enter' && handleLogin()}
            />
          </div>

          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded text-center">
              {error}
            </div>
          )}

          <Button
            onClick={handleLogin}
            disabled={!subscriptionCode || isLoading}
            className="w-full text-lg py-3"
          >
            {isLoading ? "جاري التحقق..." : "🔓 دخول"}
          </Button>

          <div className="text-center text-sm text-gray-500 mt-4">
            <p>💡 تلميح: استخدم الرمز <strong>VIP123</strong></p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function App() {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [items, setItems] = useState([
    { name: "شاي", category: "مشروب ساخن", price: 500, available: true },
    { name: "نركيلة عنب نعناع", category: "نركيلة", price: 4000, available: true },
  ]);
  const [newItem, setNewItem] = useState({ name: "", category: "", price: 0, available: true });

  const handleAddItem = () => {
    setItems([...items, newItem]);
    setNewItem({ name: "", category: "", price: 0, available: true });
  };

  const handleDeleteItem = (index: number) => {
    setItems(items.filter((_, i) => i !== index));
  };

  const handleLogin = (success: boolean) => {
    if (success) {
      setIsLoggedIn(true);
    }
  };

  const handleLogout = () => {
    setIsLoggedIn(false);
  };

  // إذا لم يسجل الدخول، اعرض شاشة تسجيل الدخول
  if (!isLoggedIn) {
    return <LoginScreen onLogin={handleLogin} />;
  }

  // واجهة إدارة المقهى الرئيسية
  return (
    <div className="p-6 max-w-4xl mx-auto text-right" dir="rtl">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">🍽️ إدارة الطاولات - مقهى VIP</h1>
        <Button
          variant="outline"
          onClick={handleLogout}
          className="text-red-600 hover:text-red-700"
        >
          🚪 تسجيل خروج
        </Button>
      </div>
      <Dialog>
        <DialogTrigger>
          <Button className="mb-4">➕ إضافة صنف جديد</Button>
        </DialogTrigger>
        <DialogContent>
          <DialogTitle>إضافة صنف</DialogTitle>
          <div className="space-y-4">
            <div>
              <Label>اسم الصنف</Label>
              <Input value={newItem.name} onChange={(e) => setNewItem({ ...newItem, name: e.target.value })} />
            </div>
            <div>
              <Label>الفئة</Label>
              <Select onValueChange={(value) => setNewItem({ ...newItem, category: value })}>
                <SelectTrigger>
                  <SelectValue placeholder="اختر فئة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="مشروب ساخن">مشروب ساخن</SelectItem>
                  <SelectItem value="مشروب بارد">مشروب بارد</SelectItem>
                  <SelectItem value="نركيلة">نركيلة</SelectItem>
                  <SelectItem value="طعام">طعام</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label>السعر (دينار)</Label>
              <Input
                type="number"
                value={newItem.price}
                onChange={(e) => setNewItem({ ...newItem, price: Number(e.target.value) })}
              />
            </div>
            <div className="flex items-center justify-between">
              <Label>متوفر؟</Label>
              <Switch
                checked={newItem.available}
                onCheckedChange={(checked) => setNewItem({ ...newItem, available: checked })}
              />
            </div>
            <Button onClick={handleAddItem}>حفظ</Button>
          </div>
        </DialogContent>
      </Dialog>

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>الاسم</TableHead>
            <TableHead>الفئة</TableHead>
            <TableHead>السعر</TableHead>
            <TableHead>متوفر</TableHead>
            <TableHead>إجراء</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {items.map((item, index) => (
            <TableRow key={index}>
              <TableCell>{item.name}</TableCell>
              <TableCell>{item.category}</TableCell>
              <TableCell>{item.price}</TableCell>
              <TableCell>{item.available ? "✅" : "❌"}</TableCell>
              <TableCell>
                <Button variant="destructive" onClick={() => handleDeleteItem(index)}>
                  🗑️ حذف
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
