import { useState } from "react";
import { But<PERSON> } from "./components/ui/button";
import { Dialog, DialogContent, DialogTitle, DialogTrigger } from "./components/ui/dialog";
import { Input } from "./components/ui/input";
import { Label } from "./components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "./components/ui/table";
import { Switch } from "./components/ui/switch";

// مكون تسجيل الدخول
function LoginScreen({ onLogin }: { onLogin: (success: boolean) => void }) {
  const [subscriptionCode, setSubscriptionCode] = useState("");
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const handleLogin = () => {
    setIsLoading(true);
    setError("");

    // محاكاة التحقق من رمز الاشتراك
    setTimeout(() => {
      if (subscriptionCode === "VIP123") {
        onLogin(true);
      } else {
        setError("رمز الاشتراك غير صحيح. جرب: VIP123");
        onLogin(false);
      }
      setIsLoading(false);
    }, 1000);
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100" dir="rtl">
      <div className="bg-white p-8 rounded-lg shadow-lg w-full max-w-md">
        <div className="text-center mb-6">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">☕ مقهى VIP</h1>
          <p className="text-gray-600">أدخل رمز الاشتراك للدخول</p>
        </div>

        <div className="space-y-4">
          <div>
            <Label htmlFor="subscription-code" className="text-right block mb-2">
              رمز الاشتراك
            </Label>
            <Input
              id="subscription-code"
              type="text"
              placeholder="أدخل رمز الاشتراك (مثال: VIP123)"
              value={subscriptionCode}
              onChange={(e) => setSubscriptionCode(e.target.value)}
              className="text-center text-lg"
              onKeyPress={(e) => e.key === 'Enter' && handleLogin()}
            />
          </div>

          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded text-center">
              {error}
            </div>
          )}

          <Button
            onClick={handleLogin}
            disabled={!subscriptionCode || isLoading}
            className="w-full text-lg py-3"
          >
            {isLoading ? "جاري التحقق..." : "🔓 دخول"}
          </Button>

          <div className="text-center text-sm text-gray-500 mt-4">
            <p>💡 تلميح: استخدم الرمز <strong>VIP123</strong></p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function App() {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [items, setItems] = useState([
    // المشروبات الساخنة
    { name: "شاي", category: "مشروب ساخن", price: 500, available: true },
    { name: "شاي دارسين", category: "مشروب ساخن", price: 750, available: true },
    { name: "قهوة تركية", category: "مشروب ساخن", price: 1000, available: true },
    { name: "نسكافيه", category: "مشروب ساخن", price: 1500, available: true },
    { name: "كابتشينو", category: "مشروب ساخن", price: 2000, available: true },

    // المشروبات الباردة
    { name: "ماء صغير", category: "مشروب بارد", price: 500, available: true },
    { name: "ماء كبير", category: "مشروب بارد", price: 1000, available: true },
    { name: "عصير برتقال طبيعي", category: "مشروب بارد", price: 2500, available: true },
    { name: "سفن أب / بيبسي / ميرندا", category: "مشروب بارد", price: 1000, available: true },

    // النراكيل
    { name: "نركيلة تفاحتين", category: "نركيلة", price: 4000, available: true },
    { name: "نركيلة عنب نعناع", category: "نركيلة", price: 4000, available: true },
    { name: "نركيلة بطيخ نعناع", category: "نركيلة", price: 4000, available: true },
    { name: "نركيلة فخفخينا", category: "نركيلة", price: 4000, available: true },

    // الطعام
    { name: "بطاطا مقلية", category: "طعام", price: 2000, available: true },
    { name: "ساندويتش شاورما", category: "طعام", price: 3000, available: true },

    // إضافات النركيلة
    { name: "معسل إضافي", category: "إضافات نركيلة", price: 2000, available: true },
    { name: "راس جديد", category: "إضافات نركيلة", price: 1000, available: true },
  ]);
  const [newItem, setNewItem] = useState({ name: "", category: "", price: 0, available: true });
  const [selectedCategory, setSelectedCategory] = useState("الكل");

  const handleAddItem = () => {
    setItems([...items, newItem]);
    setNewItem({ name: "", category: "", price: 0, available: true });
  };

  const handleDeleteItem = (index: number) => {
    setItems(items.filter((_, i) => i !== index));
  };

  const handleLogin = (success: boolean) => {
    if (success) {
      setIsLoggedIn(true);
    }
  };

  const handleLogout = () => {
    setIsLoggedIn(false);
  };

  // فلترة الأصناف حسب الفئة
  const filteredItems = selectedCategory === "الكل"
    ? items
    : items.filter(item => item.category === selectedCategory);

  // إحصائيات
  const totalItems = items.length;
  const availableItems = items.filter(item => item.available).length;
  const categories = [...new Set(items.map(item => item.category))];

  // دالة لإضافة الرموز التعبيرية للفئات
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "مشروب ساخن": return "☕";
      case "مشروب بارد": return "🥤";
      case "نركيلة": return "🚬";
      case "طعام": return "🍽️";
      case "إضافات نركيلة": return "➕";
      default: return "📦";
    }
  };

  // إذا لم يسجل الدخول، اعرض شاشة تسجيل الدخول
  if (!isLoggedIn) {
    return <LoginScreen onLogin={handleLogin} />;
  }

  // واجهة إدارة المقهى الرئيسية
  return (
    <div className="p-6 max-w-4xl mx-auto text-right" dir="rtl">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">🍽️ إدارة الطاولات - مقهى VIP</h1>
        <Button
          variant="outline"
          onClick={handleLogout}
          className="text-red-600 hover:text-red-700"
        >
          🚪 تسجيل خروج
        </Button>
      </div>

      {/* الإحصائيات */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-blue-50 p-4 rounded-lg text-center">
          <div className="text-2xl font-bold text-blue-600">{totalItems}</div>
          <div className="text-blue-800">إجمالي الأصناف</div>
        </div>
        <div className="bg-green-50 p-4 rounded-lg text-center">
          <div className="text-2xl font-bold text-green-600">{availableItems}</div>
          <div className="text-green-800">متوفر</div>
        </div>
        <div className="bg-orange-50 p-4 rounded-lg text-center">
          <div className="text-2xl font-bold text-orange-600">{categories.length}</div>
          <div className="text-orange-800">الفئات</div>
        </div>
      </div>

      {/* فلتر الفئات */}
      <div className="mb-6">
        <Label className="block mb-2">🔍 فلترة حسب الفئة:</Label>
        <div className="flex flex-wrap gap-2">
          <Button
            variant={selectedCategory === "الكل" ? "default" : "outline"}
            onClick={() => setSelectedCategory("الكل")}
            className="mb-2"
          >
            📋 الكل ({totalItems})
          </Button>
          {categories.map((category) => (
            <Button
              key={category}
              variant={selectedCategory === category ? "default" : "outline"}
              onClick={() => setSelectedCategory(category)}
              className="mb-2"
            >
              {getCategoryIcon(category)} {category} ({items.filter(item => item.category === category).length})
            </Button>
          ))}
        </div>
      </div>
      <Dialog>
        <DialogTrigger>
          <Button className="mb-4">➕ إضافة صنف جديد</Button>
        </DialogTrigger>
        <DialogContent>
          <DialogTitle>إضافة صنف</DialogTitle>
          <div className="space-y-4">
            <div>
              <Label>اسم الصنف</Label>
              <Input value={newItem.name} onChange={(e) => setNewItem({ ...newItem, name: e.target.value })} />
            </div>
            <div>
              <Label>الفئة</Label>
              <Select onValueChange={(value) => setNewItem({ ...newItem, category: value })}>
                <SelectTrigger>
                  <SelectValue placeholder="اختر فئة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="مشروب ساخن">☕ مشروب ساخن</SelectItem>
                  <SelectItem value="مشروب بارد">🥤 مشروب بارد</SelectItem>
                  <SelectItem value="نركيلة">🚬 نركيلة</SelectItem>
                  <SelectItem value="طعام">🍽️ طعام</SelectItem>
                  <SelectItem value="إضافات نركيلة">➕ إضافات نركيلة</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label>السعر (دينار)</Label>
              <Input
                type="number"
                value={newItem.price}
                onChange={(e) => setNewItem({ ...newItem, price: Number(e.target.value) })}
              />
            </div>
            <div className="flex items-center justify-between">
              <Label>متوفر؟</Label>
              <Switch
                checked={newItem.available}
                onCheckedChange={(checked) => setNewItem({ ...newItem, available: checked })}
              />
            </div>
            <Button onClick={handleAddItem}>حفظ</Button>
          </div>
        </DialogContent>
      </Dialog>

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>الاسم</TableHead>
            <TableHead>الفئة</TableHead>
            <TableHead>السعر</TableHead>
            <TableHead>متوفر</TableHead>
            <TableHead>إجراء</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {filteredItems.map((item, index) => {
            const originalIndex = items.findIndex(originalItem =>
              originalItem.name === item.name &&
              originalItem.category === item.category &&
              originalItem.price === item.price
            );
            return (
              <TableRow key={`${item.name}-${item.category}-${index}`}>
                <TableCell className="font-medium">{item.name}</TableCell>
                <TableCell>
                  <span className="inline-flex items-center gap-1">
                    {getCategoryIcon(item.category)} {item.category}
                  </span>
                </TableCell>
                <TableCell className="font-bold text-green-600">
                  {item.price.toLocaleString()} د.ع
                </TableCell>
                <TableCell>
                  <Switch
                    checked={item.available}
                    onCheckedChange={(checked) => {
                      const newItems = [...items];
                      newItems[originalIndex].available = checked;
                      setItems(newItems);
                    }}
                  />
                  <span className="mr-2">
                    {item.available ? "✅ متوفر" : "❌ غير متوفر"}
                  </span>
                </TableCell>
                <TableCell>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => handleDeleteItem(originalIndex)}
                  >
                    🗑️ حذف
                  </Button>
                </TableCell>
              </TableRow>
            );
          })}
          {filteredItems.length === 0 && (
            <TableRow>
              <TableCell colSpan={5} className="text-center py-8 text-gray-500">
                <div className="flex flex-col items-center gap-2">
                  <div className="text-4xl">📭</div>
                  <div>لا توجد أصناف في هذه الفئة</div>
                </div>
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>

      {/* معلومات إضافية */}
      <div className="mt-6 text-center text-gray-600">
        <p>عرض {filteredItems.length} من {totalItems} صنف</p>
      </div>
    </div>
  );
}
